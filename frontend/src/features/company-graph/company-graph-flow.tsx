import { memo, useRef, useState } from 'react'
import {
    ReactFlow,
    useNodesState,
    useEdgesState,
    type Edge,
    ConnectionLineType,
} from '@xyflow/react'

import { useViewportManagement } from './hooks/use-viewport-management'
import type { EmployeeNode as EmployeeNodeType } from './models/types'
import { EmployeeNode } from './ui/employee-node'
import { useGraphLayout } from './hooks/use-graph-layout'
import { CompanyGraphLoading } from './ui/company-graph-loading'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/shared/ui/kit/dialog'
import { Button } from '@/shared/ui/kit/button'

const nodeTypes = {
    employee: EmployeeNode,
} as const

interface CompanyGraphFlowProps {
    initialNodes: EmployeeNodeType[]
    initialEdges: Edge[]
    className?: string
}

export const CompanyGraphFlow = memo(function CompanyGraphFlow({
    initialNodes,
    initialEdges,
    className = '',
}: CompanyGraphFlowProps) {
    const { nodes: layoutedNodes, edges: layoutedEdges } = useGraphLayout(
        initialNodes,
        initialEdges,
        { nodeHeight: 150, nodeWidth: 240 },
    )
    // TODO Имплементировать реальную интерактивность для диалога
    const [dialogOpen, setDialogOpen] = useState(false)
    const containerRef = useRef(null)

    const [
        nodes,
        ,
        onNodesChange,
    ] = useNodesState(layoutedNodes)
    const [edges] = useEdgesState(layoutedEdges)

    const { focusOnRootNode, isViewportLoading } = useViewportManagement({
        padding: 5,
        rootNodePositionRatio: 0.7,
    })

    return (
        <div
            ref={containerRef}
            className={` w-full h-full bg-elevated-background rounded-xl relative ${className}`}
        >
            {isViewportLoading && <CompanyGraphLoading />}
            {/* TODO Заменить цвета на дизайн токены
        TODO Имплементировать реальную интерактивность для диалога
    */}
            <ReactFlow
                nodes={nodes}
                edges={edges}
                onNodesChange={onNodesChange}
                nodesDraggable={false}
                onNodeClick={() => setDialogOpen(true)}
                nodeTypes={nodeTypes}
                nodesConnectable={false}
                proOptions={{ hideAttribution: true }}
                defaultEdgeOptions={{
                    markerEnd: { type: 'arrow', color: '#768099' },
                    type: ConnectionLineType.SmoothStep,
                    style: { strokeWidth: 1, stroke: '#768099' },
                    selectable: false,
                }}
                minZoom={0.5}
                maxZoom={1.5}
                onInit={focusOnRootNode}
            ></ReactFlow>
            <Dialog
                open={dialogOpen}
                onOpenChange={setDialogOpen}
            >
                <DialogContent
                    container={containerRef.current}
                    className="backdrop-blur-[10px] min-w-sm bg-white/50 border-white shadow-[0px_0px_40px_0px_rgba(0,0,0,0.1)] rounded-2xl p-6"
                >
                    <div className="flex flex-col gap-6">
                        <DialogHeader className="text-left">
                            <DialogTitle className="text-2xl font-medium text-[#3e4045] leading-normal">
                                Фёдор Авдеев
                            </DialogTitle>
                        </DialogHeader>

                        <div className="flex flex-col gap-3">
                            <div className="flex flex-row items-center">
                                <div className="w-1.5 h-1.5 ml-2 mr-4 rounded-full bg-blue-500" />
                                <a className="text-sm">Перед тем, как приступить к заданию</a>
                            </div>
                            <div className="flex flex-row items-center">
                                <div className="w-1.5 h-1.5 ml-2 mr-4 rounded-full bg-blue-500" />
                                <a className="text-sm"> Перед тем, как приступить к заданию</a>
                            </div>
                            <div className="flex flex-row items-center">
                                <div className="w-1.5 h-1.5 ml-2 mr-4 rounded-full bg-blue-500" />
                                <a className="text-sm"> Перед тем, как приступить к заданию</a>
                            </div>
                        </div>

                        <Button
                            className="self-start"
                            variant={'primary'}
                            size={'default'}
                        >
                            Профиль сотрудника
                        </Button>
                    </div>
                </DialogContent>
            </Dialog>
        </div>
    )
})
